// "use client"
// import React, { useState, useEffect } from 'react';
// import { Swiper, SwiperSlide } from 'swiper/react';
// import { Navigation, Pagination } from 'swiper/modules';
// import 'swiper/css';
// import 'swiper/css/navigation';
// import 'swiper/css/pagination';
// import SEOOptimizedImage from '@/components/Image/SEOOptimizedImage';
// import { useTranslations } from 'next-intl';
// import { motion } from 'framer-motion';
// import { Link } from '@/navigation';

// const Card = ({ title, imgSrc, hrefs }) => {
//     const t = useTranslations();
//     return (
//         <div className="group relative overflow-hidden">
//             <div className="relative w-full">
//                 <div className='group'>
//                     <SEOOptimizedImage
//                         src={imgSrc}
//                         alt={title}
//                         width={600}
//                         height={800}
//                         quality={100}
//                         className="w-full h-auto max-h-[700px] aspect-[16/19] object-cover transition-transform duration-700"
//                         priority
//                     />
//                     {/* 玻璃滑过效果 - 只用于图片 */}
//                     {/* <div className="absolute inset-0 -translate-x-full group-hover:translate-x-full transition-transform duration-[700ms] ease-out">
//                         <div className="h-full w-1/3 bg-gradient-to-r from-transparent via-white/30 to-transparent transform skew-x-12 blur-sm"></div>
//                     </div> */}
//                 </div>
//                 <div className="absolute inset-x-0 bottom-0 pb-8 bg-gradient-to-t from-black/90 via-black/50 to-transparent">
//                     <div className="px-6 transform group-hover:translate-y-[-40px] transition-transform duration-300">
//                         <h3 className="text-3xl text-white mb-6">{title}</h3>
//                         <Link href={hrefs} className="px-6 py-2 bg-[#83c000] hover:bg-opacity-80 hover:text-white text-white rounded text-sm irs absolute opacity-0 group-hover:opacity-100 transition-opacity duration-300">
//                             {t("banner.Shopnow")}
//                         </Link>
//                     </div>
//                 </div>
//             </div>
//         </div>
//     );
// };

// export default function GetReadyToPlay() {
//     const t = useTranslations();
//     const [paddingLeft, setPaddingLeft] = useState(0);
//     const [swiper, setSwiper] = useState(null);
//     const cardData = [
//         {
//             title: t('service.paddle'),
//             imgSrc: '/image/home/<USER>',
//             hrefs: "/products/paddle"
//         },
//         {
//             title: t('service.balls'),
//             imgSrc: '/image/home/<USER>',
//             hrefs: "/products/ball"
//         },
//         {
//             title: t('service.accessories'),
//             imgSrc: '/image/home/<USER>',
//             hrefs: "/products/accessories"
//         },
//         {
//             title: t('service.bags'),
//             imgSrc: '/image/home/<USER>',
//             hrefs: "/products/bags"
//         },
//         {
//             title: t('service.genius'),
//             imgSrc: '/image/home/<USER>',
//             hrefs: "/products/apparel"
//         },
//     ];
//     useEffect(() => {
//         const calculatePadding = () => {
//             const windowWidth = window.innerWidth;
//             const maxWidth = 1460;
//             const containerWidth = Math.min(windowWidth, maxWidth);
//             const sideMargin = (windowWidth - containerWidth) / 2;

//             const rem = parseFloat(getComputedStyle(document.documentElement).fontSize);
//             // The container has `px-4`, which is `1rem`.
//             const containerPadding = rem * 1;

//             setPaddingLeft(sideMargin + containerPadding);
//         };

//         calculatePadding();
//         window.addEventListener('resize', calculatePadding);

//         return () => {
//             window.removeEventListener('resize', calculatePadding);
//         };
//     }, []);

//     return (
//         <section className="py-16 md:py-28 overflow-hidden">
//             <div style={{ paddingLeft: `${paddingLeft}px` }}>
//                 <div className="flex items-center justify-between mb-6 md:mb-8 lg:mb-12">
//                     <motion.h2
//                         className="text-xl md:text-2xl 2xl:text-4xl uppercase"
//                         initial={{ opacity: 0, y: 20 }}
//                         whileInView={{ opacity: 1, y: 0 }}
//                         viewport={{ once: true }}
//                         transition={{ duration: 0.5 }}
//                     >
//                         {t('service.title')}
//                     </motion.h2>
//                     <div className="flex gap-2 md:gap-3 lg:gap-4 pr-4">
//                         <button
//                             onClick={() => swiper?.slidePrev()}
//                             className="w-10 h-10 max-md:w-8 max-md:h-8 border border-gray-300 flex items-center justify-center hover:bg-gray-100 transition-colors"
//                             aria-label="Previous slide"
//                         >
//                             <svg
//                                 width="24"
//                                 height="24"
//                                 className="w-5 h-5 md:w-6 md:h-6 lg:w-7 lg:h-7"
//                                 viewBox="0 0 24 24"
//                                 fill="none"
//                                 xmlns="http://www.w3.org/2000/svg"
//                             >
//                                 <path d="M15 19L8 12L15 5" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
//                             </svg>
//                         </button>
//                         <button
//                             onClick={() => swiper?.slideNext()}
//                             className="w-10 h-10 max-md:w-8 max-md:h-8 border border-gray-300 flex items-center justify-center hover:bg-gray-100 transition-colors"
//                             aria-label="Next slide"
//                         >
//                             <svg
//                                 width="24"
//                                 height="24"
//                                 className="w-5 h-5 md:w-6 md:h-6 lg:w-7 lg:h-7"
//                                 viewBox="0 0 24 24"
//                                 fill="none"
//                                 xmlns="http://www.w3.org/2000/svg"
//                             >
//                                 <path d="M9 5L16 12L9 19" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
//                             </svg>
//                         </button>
//                     </div>
//                 </div>
//             </div>

//             <div style={{ paddingLeft: `${paddingLeft}px` }}>
//                 <Swiper
//                     modules={[Navigation, Pagination]}
//                     spaceBetween={20}
//                     slidesPerView={1.2}
//                     onSwiper={setSwiper}
//                     pagination={{
//                         type: "progressbar",
//                     }}
//                     breakpoints={{
//                         480: { slidesPerView: 1.3 },
//                         640: { slidesPerView: 1.5, spaceBetween: 24 },
//                         768: { slidesPerView: 2.2, spaceBetween: 24 },
//                         1024: { slidesPerView: 2.5, spaceBetween: 30 },
//                         1280: { slidesPerView: 3.5, spaceBetween: 30 },
//                     }}
//                     className="!pb-12 index-product-cate-swiper-container"
//                 >
//                     {cardData.map((card, index) => (
//                         <SwiperSlide key={index}>
//                             <Card {...card} />
//                         </SwiperSlide>
//                     ))}
//                 </Swiper>
//             </div>
//         </section>
//     );
// }









"use client"
import React, { useState, useEffect } from 'react';
import { Swiper, SwiperSlide } from 'swiper/react';
import { Navigation, Pagination } from 'swiper/modules';
import 'swiper/css';
import 'swiper/css/navigation';
import 'swiper/css/pagination';
import SEOOptimizedImage from '@/components/Image/SEOOptimizedImage';
import { useTranslations,useLocale } from 'next-intl';
import { motion } from 'framer-motion';
import { Link } from '@/navigation';
import { defaultLocale } from '@/config';

const Card = ({ title, imgSrc, hrefs }) => {
    const locale = useLocale();
    const t = useTranslations();
    return (
        <div className="group relative overflow-hidden">
            <div className="relative w-full">
                <div className='group'>
                    <SEOOptimizedImage
                        src={imgSrc}
                        alt={title}
                        width={600}
                        height={800}
                        quality={100}
                        className="w-full h-auto max-h-[700px] aspect-[16/24] object-cover transition-transform duration-700"
                        priority
                    />
                    {/* 玻璃滑过效果 - 只用于图片 */}
                    {/* <div className="absolute inset-0 -translate-x-full group-hover:translate-x-full transition-transform duration-[700ms] ease-out">
                        <div className="h-full w-1/3 bg-gradient-to-r from-transparent via-white/30 to-transparent transform skew-x-12 blur-sm"></div>
                    </div> */}
                </div>
                <div className="absolute inset-x-0 bottom-0 pb-8 bg-gradient-to-t from-black/90 via-black/50 to-transparent">
                    <div className="px-6 transform group-hover:translate-y-[-40px] transition-transform duration-300">
                        <h3 className={`text-xl text-white mb-4 ${locale === defaultLocale ? "ib" : "font-semibold"}`}>{title}</h3>
                        <Link href={hrefs} className="px-6 py-2 bg-[#83c000] hover:bg-opacity-80 hover:text-white text-white rounded text-sm irs absolute opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                            {t("banner.Shopnow")}
                        </Link>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default function GetReadyToPlay() {
    const locale = useLocale();
    const t = useTranslations();
    const [paddingLeft, setPaddingLeft] = useState(0);
    const [swiper, setSwiper] = useState(null);
    const cardData = [
        {
            title: t('service.paddle'),
            imgSrc: '/image/home/<USER>',
            hrefs: "/products/paddle"
        },
        {
            title: t('service.balls'),
            imgSrc: '/image/home/<USER>',
            hrefs: "/products/ball"
        },
        {
            title: t('service.accessories'),
            imgSrc: '/image/home/<USER>',
            hrefs: "/products/accessories"
        },
        {
            title: t('service.bags'),
            imgSrc: '/image/home/<USER>',
            hrefs: "/products/bags"
        },
        {
            title: t('service.genius'),
            imgSrc: '/image/home/<USER>',
            hrefs: "/products/apparel"
        },
    ];
    useEffect(() => {
        const calculatePadding = () => {
            const windowWidth = window.innerWidth;
            const maxWidth = 1460;
            const containerWidth = Math.min(windowWidth, maxWidth);
            const sideMargin = (windowWidth - containerWidth) / 2;

            const rem = parseFloat(getComputedStyle(document.documentElement).fontSize);
            // The container has `px-4`, which is `1rem`.
            const containerPadding = rem * 1;

            setPaddingLeft(sideMargin + containerPadding);
        };

        calculatePadding();
        window.addEventListener('resize', calculatePadding);

        return () => {
            window.removeEventListener('resize', calculatePadding);
        };
    }, []);

    return (
        <section className="pt-16 md:pt-28 pb-10 md:pb-20 overflow-hidden">
            <div className="flex items-center justify-between mb-6 md:mb-8 lg:mb-12 container">
                <h2
                    className={`text-xl md:text-2xl 2xl:text-4xl uppercase  ${locale === defaultLocale ? "ib" : "font-semibold"}`}
                >
                    {t('service.title')}
                </h2>
                <div className="flex gap-2 md:gap-3 lg:gap-4 pr-4">
                </div>
            </div>
            {/* style={{ paddingLeft: `${paddingLeft}px` }} */}
            <div className={`grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3  2xl:grid-cols-5 gap-6  container`}>
                {cardData.map((card, index) => (
                    <Card {...card} />
                ))}
            </div>
        </section>
    );
}