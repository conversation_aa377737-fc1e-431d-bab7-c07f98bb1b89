"use client";
import React from "react";
import { Modal } from "antd";
import { useTranslations, useLocale } from "next-intl";
import { ProductListItemFragment } from "@/gql/graphql";
import { filterCateImg } from "../Product/product-card";
import SEOOptimizedImage from "@/components/Image/SEOOptimizedImage";
import { defaultLocale } from "@/config";
import { useProductStore } from "@/lib/store/product.store";
import useIsMobile from "@/lib/hooks/useIsMobile";
import { Link } from "@/navigation"
interface CompareModalProps {
  open: boolean;
  onClose: () => void;
  compareList: any[];
  onRemoveProduct: (id: string) => void;
  onCloseDrawer?: () => void; // 新增：关闭比较抽屉的回调
}

const CompareModal: React.FC<CompareModalProps> = ({
  open,
  onClose,
  compareList,
  onRemoveProduct,
  onCloseDrawer,
}) => {
  const t = useTranslations();
  const locale = useLocale();
  const { currencyUnit } = useProductStore();
  const isMobile = useIsMobile();
  console.log("compareList----",compareList);
  
  // 渲染价格
  const renderPrice = (price: any) => {
    if (!price?.start || !price?.stop) return "N/A";

    try {
      const startPrice = price.start.gross;
      const stopPrice = price.stop.gross;

      const isSamePrice = Number(startPrice.amount) === Number(stopPrice.amount);

      const formatPrice = (amount: any) => {
        return typeof amount === 'number'
          ? amount.toFixed(2)
          : typeof amount === 'string'
            ? parseFloat(amount).toFixed(2)
            : '0.00';
      };

      if (isSamePrice) {
        return `$ ${formatPrice(startPrice.amount)}`;
      } else {
        return `$ ${formatPrice(startPrice.amount)} - ${formatPrice(stopPrice.amount)}`;
      }
    } catch (error) {
      return "N/A";
    }
  };

  // 获取产品属性
  const getProductAttributes = (product: ProductListItemFragment) => {
    const attributes = product?.attributes?.map((item) => ({
      label: item.attribute?.translation?.name || item.attribute.name,
      value: item.values.map((i) => i.translation?.name || i.name).join(", "),
    })) || [];

    const variants = product?.variants?.map((variant) => ({
      name: variant?.translation?.name || variant?.name,
      sku: variant?.sku,
      weight: variant?.weight ? `${variant.weight.value} ${variant.weight.unit}` : null,
    })) || [];

    return { attributes, variants };
  };

  // 获取所有唯一的属性标签
  const getAllAttributeLabels = () => {
    // 按照设计图的顺序定义属性标签，使用多语言翻译
    const orderedLabels = [
      t("common.Color"),
      "SKU",
      t("common.Price (USD)"),
      t("common.Surface Material"),
      t("common.Core Material"),
      t("common.Finishing"),
      t("common.Printing"),
      t("common.Edge Guard"),
      t("common.Thickness"),
      t("common.Weight"),
      t("common.Length"),
      t("common.Width"),
      t("common.Grip Length"),
      t("common.Performance Type"),
      t("common.Power Rating"),
      t("common.Control Rating"),
      t("common.Spin Rating")
    ];

    return orderedLabels;
  };

  // 标签名称到 slug 的映射关系
  const getLabelToSlugMapping = () => {
    return {
      "Color": "color",
      "Surface Material": "surface-material",
      "Core Material": "core-technology",
      "Finishing": "finishing",
      "Printing": "printing",
      "Edge Guard": "edge-guard",
      "Thickness": "thickness",
      "Weights": "weights",
      "Length": "length-inch",
      "Width": "width-inch",
      "Grip Length": "grip-length-inch",
      "Performance Type": "performance-type",
      "Power Rating": "power-rating",
      "Control Rating": "control-rating",
      "Spin Rating": "spin-rating"
    };
  };

  // 获取产品的特定属性值
  const getAttributeValue = (product: ProductListItemFragment, label: string) => {
    if (label === "Price (USD)") {
      return renderPrice(product.pricing?.priceRange);
    }

    if (label === "SKU") {
      return product.variants?.[0]?.sku || "-";
    }

    if (label === "Weight") {
      const weight = product.variants?.[0]?.weight;
      return weight ? `${weight.value}${weight.unit}` : "-";
    }

    // 特殊处理 Color 属性 - 从变体的 attributes 中获取
    if (label === "Color") {
      // 遍历所有变体，查找 Color 属性
      for (const variant of product.variants || []) {
        for (const attr of variant.attributes || []) {
          if (attr.attribute?.name === "Color" || attr.attribute?.translation?.name === "Color") {
            // 获取颜色值
            const colorValue = attr.values?.[0]?.name || attr.values?.[0]?.translation?.name;
            if (colorValue) {
              return colorValue;
            }
          }
        }
      }
      // 如果在变体中找不到，尝试从产品级别的 attributes 中查找
      const { attributes } = getProductAttributes(product);
      const colorAttr = attributes.find((a) => a.label === "Color");
      return colorAttr?.value || "-";
    }

    // 获取标签到 slug 的映射
    const labelToSlugMapping = getLabelToSlugMapping();
    const targetSlug = labelToSlugMapping[label];

    if (targetSlug) {
      // 根据 slug 查找对应的属性
      const matchedAttribute = product?.attributes?.find(attr =>
        attr.attribute?.slug === targetSlug
      );

      if (matchedAttribute) {
        // 获取属性值，优先使用翻译
        const values = matchedAttribute.values?.map(value =>
          value.translation?.name || value.name
        ).join(", ");
        return values || "-";
      }
    }

    // 如果没有找到对应的 slug，使用原来的方法作为后备
    const { attributes } = getProductAttributes(product);
    const attr = attributes.find((a) => a.label === label);
    return attr?.value || "-";
  };

  // 渲染特殊属性值（如评分）
  const renderAttributeValue = (product: ProductListItemFragment, label: string, value: string) => {
    // 如果是评分相关的属性，渲染进度条样式
    if (label.toLowerCase().includes('rating') || label.toLowerCase().includes('power') || label.toLowerCase().includes('control') || label.toLowerCase().includes('spin')) {
      // 尝试解析数值并渲染进度条
      const numValue = parseFloat(value);
      if (!isNaN(numValue) && numValue >= 0) {
        // 根据不同的评分类型设置最大值
        let maxValue = 10; // 默认最大值为10

        // 可以根据具体的评分类型调整最大值
        if (label.toLowerCase().includes('power')) {
          maxValue = 10; // Power Rating 最大值为10
        } else if (label.toLowerCase().includes('control')) {
          maxValue = 10; // Control Rating 最大值为10
        } else if (label.toLowerCase().includes('spin')) {
          maxValue = 10; // Spin Rating 最大值为10
        }

        // 确保数值不超过最大值
        const clampedValue = Math.min(numValue, maxValue);
        const percentage = (clampedValue / maxValue) * 100;

        return (
          <div className="flex justify-center w-full">
            <div className={`${isMobile ? 'w-[80px]' : 'w-[120px]'}`}>
              <div className={`flex items-center bg-white border-black border rounded ${isMobile ? 'h-3' : 'h-4'} overflow-hidden`}>
                <div
                  className="bg-black h-full transition-all duration-300 rounded"
                  style={{ width: `${percentage}%` }}
                ></div>
                <div
                  className="bg-white h-full flex-1"
                ></div>
              </div>
            </div>
          </div>
        );
      }
    }

    // 特殊处理颜色属性
    if (label.toLowerCase() === 'color') {
      // 如果值是横杠，直接返回横杠
      if (value === '-') {
        return <span className={`${isMobile ? 'text-xs' : 'text-sm'} font-medium text-black text-center`}>-</span>;
      }

      // 检查是否是16进制颜色值
      const isHexColor = /^#[0-9A-Fa-f]{6}$/.test(value);

      if (isHexColor) {
        return (
          <div className="flex items-center justify-center">
            <div
              className="w-14 h-5"
              style={{ backgroundColor: value }}
            ></div>
          </div>
        );
      } else {
        // 如果不是16进制颜色，尝试将颜色名称转换为颜色
        const colorMap: { [key: string]: string } = {
          'red': '#FF0000',
          'blue': '#0000FF',
          'green': '#008000',
          'yellow': '#FFFF00',
          'black': '#000000',
          'white': '#FFFFFF',
          'gray': '#808080',
          'grey': '#808080',
          'orange': '#FFA500',
          'purple': '#800080',
          'pink': '#FFC0CB',
          'brown': '#A52A2A'
        };

        const colorValue = colorMap[value.toLowerCase()];
        if (colorValue) {
          return (
            <div className="flex items-center justify-center">
              <div
                className="w-14 h-5"
                style={{ backgroundColor: colorValue }}
              ></div>
            </div>
          );
        } else {
          // 如果无法匹配颜色，显示横杠
          return <span className={`${isMobile ? 'text-xs' : 'text-sm'} font-medium text-black text-center`}>-</span>;
        }
      }
    }

    return <span className={`${isMobile ? 'text-xs' : 'text-sm'} font-medium text-black text-center`}>{value}</span>;
  };

  const attributeLabels = getAllAttributeLabels();

  return (
    <Modal
      open={open}
      onCancel={onClose}
      footer={null}
      width={isMobile ? "100%" : "90%"}
      style={{
        maxWidth: isMobile ? "100%" : "900px",
        margin: isMobile ? 0 : "auto",
        top: isMobile ? 0 : 20
      }}
      className="compare-modal"
      title={
        <div className="flex items-center justify-center relative">
          <h3 className={`${isMobile ? 'text-lg' : 'text-[34px]'} mb-8 font-semibold text-center`}>Product Comparison</h3>
          <button
            onClick={onClose}
            className={`absolute right-0 top-0 text-black hover:text-gray-700 ${isMobile ? 'text-xl' : 'text-2xl'}`}
          >
            ×
          </button>
        </div>
      }
      closable={false}
    >
      <div className={`${isMobile ? 'max-h-[90vh]' : 'max-h-[80vh]'}`}>
        {/* 使用单一表格结构，头部固定 */}
        <div className="border-r border-b border-gray-200">
          <div className={`overflow-y-auto ${isMobile ? 'max-h-[80vh]' : 'max-h-[70vh]'}`}>
            <table className="w-full table-fixed">
              <colgroup>
                <col className={`${isMobile ? 'w-24' : 'w-40 md:w-48'}`} />
                <col className="w-1/2" />
                <col className="w-1/2" />
              </colgroup>

              {/* 固定的产品信息头部 */}
              <thead className="sticky top-0 z-10">
                <tr className="bg-white border-b border-gray-200">
                  {/* 空白单元格 */}
                  <th className="border-r border-gray-200 pt-4"></th>

                  {/* 产品单元格 */}
                  {[0, 1].map((index) => {
                    const item = compareList[index];

                    if (!item?.node) {
                      return (
                        <th key={`empty-${index}`} className={`border-gray-200 ${isMobile ? 'p-2 px-3' : 'p-4'} pt-4 bg-white ${index === 0 ? 'border-r' : 'border-r'}`}>
                          <div className={`flex flex-col items-center justify-center ${isMobile ? 'py-2' : 'py-4'}`}>
                            {/* 空产品占位符 */}
                            <div className={`${isMobile ? 'w-12 h-12' : 'w-16 h-16 md:w-20 md:h-20'} rounded-lg border-2 border-dashed border-gray-300 flex items-center justify-center mb-3`}>
                              {/* <i className="ri-add-line text-xl text-gray-400"></i> */}
                            </div>
                            <span className={`${isMobile ? 'text-[10px]' : 'text-xs'} font-medium text-black text-center`}>Add Product</span>
                          </div>
                        </th>
                      );
                    }

                    const product = item.node as ProductListItemFragment;
                    const imgs = filterCateImg(product.metadata);
                    const productName = locale === defaultLocale
                      ? product.name
                      : product.translation?.name || product.name;

                    return (
                      <th key={product.id} className={`border-gray-200 ${isMobile ? 'p-2 px-3' : 'p-4 px-10'} pt-4 relative bg-white ${index === 0 ? 'border-r' : 'border-r'} overflow-visible`}>
                        <div className="flex flex-col items-center justify-center">
                          {/* 产品图片 */}
                          <div className={`w-full ${isMobile ? 'h-[100px]' : 'h-[220px]'} rounded-lg border border-[#e5e5e5] mb-3 relative`}>
                            <SEOOptimizedImage
                              src={imgs[0]?.url || "/image/default-image.webp"}
                              alt={productName}
                              width={200}
                              height={200}
                              className="w-full h-full object-cover rounded-lg"
                            />
                            {/* 删除按钮 */}
                            <button
                              onClick={() => onRemoveProduct(product.id)}
                              className={`absolute -top-2 -right-2 flex ${isMobile ? 'h-6 w-6' : 'h-7 w-7'} items-center justify-center rounded-full bg-white border text-black shadow-lg transition-all duration-200 z-20`}
                            >
                              <i className={`ri-close-line ${isMobile ? 'text-sm' : 'text-lg'} !font-normal`}></i>
                            </button>
                          </div>

                          {/* 产品名称 */}
                          <h3 className={`${isMobile ? 'text-[10px]' : 'text-xs'} font-medium text-black text-center line-clamp-2 mb-3 px-1 leading-tight`}>
                            {productName}
                          </h3>

                          {/* View Details 按钮 */}
                          <Link
                            href={`/product/${product.slug}`}
                            className={`${isMobile ? 'px-3 py-2 text-[10px]' : 'px-5 py-3 text-xs'} bg-[#83c000] text-white hover:text-white rounded-full !font-normal hover:bg-opacity-80 transition-colors`}
                            onClick={() => {
                              // 关闭比较模态框
                              onClose();
                              // 关闭比较抽屉
                              onCloseDrawer?.();
                            }}
                          >
                            {t("order.View Details")}
                          </Link>
                        </div>
                      </th>
                    );
                  })}
                </tr>
              </thead>

              {/* 属性对比表格主体 */}
              <tbody>
                {attributeLabels.map((label, index) => (
                  <tr
                    key={label}
                    className={`${index % 2 === 0 ? "bg-white" : "bg-white"
                      } border-b border-[#e5e5e5] last:border-b-0`}
                  >
                    {/* 属性标签单元格 */}
                    <td className={`border-r border-[#e5e5e5] ${isMobile ? 'px-2 py-2' : 'px-4 py-3'}`}>
                      <span className={`${isMobile ? 'text-xs' : 'text-sm'} font-medium text-black`}>
                        {label}
                      </span>
                    </td>

                    {/* 产品属性值单元格 */}
                    {[0, 1].map((productIndex) => {
                      const item = compareList[productIndex];

                      if (!item?.node) {
                        return (
                          <td key={`empty-${label}-${productIndex}`} className={`border-[#e5e5e5] text-black ${isMobile ? 'px-2 py-2' : 'px-4 py-3'} text-center ${productIndex === 0 ? 'border-r' : 'border-r'}`}>
                            <span className={`${isMobile ? 'text-xs' : 'text-sm'} font-medium text-black`}>-</span>
                          </td>
                        );
                      }

                      const product = item.node as ProductListItemFragment;
                      const value = getAttributeValue(product, label);

                      return (
                        <td key={`${product.id}-${label}`} className={`border-[#e5e5e5] ${isMobile ? 'px-2 py-2' : 'px-4 py-3'} text-center ${productIndex === 0 ? 'border-r' : 'border-r'}`}>
                          {renderAttributeValue(product, label, value)}
                        </td>
                      );
                    })}
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </Modal>
  );
};

export default CompareModal;
